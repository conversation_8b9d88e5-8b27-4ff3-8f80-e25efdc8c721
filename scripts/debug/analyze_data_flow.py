#!/usr/bin/env python3
"""
从头分析数据处理流程，找出问题所在
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import pandas as pd
import random
from pathlib import Path
from src.utils.config_loader import load_config

def analyze_raw_trajectory_file(file_path):
    """分析原始轨迹文件"""
    print(f"\n{'='*60}")
    print(f"分析原始轨迹文件: {file_path}")
    print(f"{'='*60}")
    
    df = pd.read_csv(file_path)
    total_len = len(df)
    
    print(f"原始数据长度: {total_len} 个点")
    print(f"时间范围: {total_len} 秒 ({total_len/60:.1f} 分钟)")
    
    # 检查时间戳
    if 'timestamp_ms' in df.columns:
        timestamps = df['timestamp_ms'].values
        time_diff = (timestamps[-1] - timestamps[0]) / 1000.0  # 转换为秒
        print(f"实际时间跨度: {time_diff:.1f} 秒 ({time_diff/60:.1f} 分钟)")
        
        # 检查时间间隔
        time_intervals = np.diff(timestamps) / 1000.0
        print(f"时间间隔统计: 平均={np.mean(time_intervals):.2f}s, 最小={np.min(time_intervals):.2f}s, 最大={np.max(time_intervals):.2f}s")
    
    # 检查坐标范围
    if 'x' in df.columns and 'y' in df.columns:
        x_range = df['x'].max() - df['x'].min()
        y_range = df['y'].max() - df['y'].min()
        print(f"坐标范围: X={x_range:.1f}m, Y={y_range:.1f}m")
    
    print(f"数据列: {list(df.columns)}")
    print(f"前5行数据:")
    print(df.head())
    
    return df, total_len

def simulate_mask_generation(total_len, config, file_path):
    """模拟掩码生成过程"""
    print(f"\n{'='*60}")
    print(f"模拟掩码生成过程")
    print(f"{'='*60}")
    
    data_cfg = config.data_preprocessing
    min_obs_duration_s = data_cfg.masking.min_duration_min * 60
    max_obs_duration_s = data_cfg.masking.max_duration_min * 60
    min_gap_duration_s = data_cfg.masking.min_gap_duration_min * 60
    max_gap_duration_s = data_cfg.masking.max_gap_duration_min * 60
    
    print(f"配置参数:")
    print(f"  观测窗口: {min_obs_duration_s}-{max_obs_duration_s} 秒 ({min_obs_duration_s/60:.1f}-{max_obs_duration_s/60:.1f} 分钟)")
    print(f"  间歇窗口: {min_gap_duration_s}-{max_gap_duration_s} 秒 ({min_gap_duration_s/60:.1f}-{max_gap_duration_s/60:.1f} 分钟)")
    print(f"  轨迹总长度: {total_len} 秒 ({total_len/60:.1f} 分钟)")
    
    # 设置随机种子（模拟实际处理）
    file_seed = hash(str(file_path)) % (2**32)
    random.seed(file_seed)
    print(f"  文件种子: {file_seed}")
    
    # 生成掩码
    full_trajectory_mask = np.zeros(total_len, dtype=np.float32)
    current_idx = 0
    is_obs_period = True  # 确保从观测期开始
    
    segments = []  # 记录所有段落
    
    print(f"\n掩码生成过程:")
    step = 0
    while current_idx < total_len:
        step += 1
        if is_obs_period:
            duration = random.randint(min_obs_duration_s, max_obs_duration_s)
            end_idx = min(current_idx + duration, total_len)
            full_trajectory_mask[current_idx:end_idx] = 1.0
            actual_duration = end_idx - current_idx
            segments.append(('观测', current_idx, end_idx-1, actual_duration))
            print(f"  步骤{step}: 观测期 t={current_idx}-{end_idx-1} (计划{duration}s, 实际{actual_duration}s)")
            current_idx = end_idx
        else:
            duration = random.randint(min_gap_duration_s, max_gap_duration_s)
            end_idx = min(current_idx + duration, total_len)
            actual_duration = end_idx - current_idx
            segments.append(('间歇', current_idx, end_idx-1, actual_duration))
            print(f"  步骤{step}: 间歇期 t={current_idx}-{end_idx-1} (计划{duration}s, 实际{actual_duration}s)")
            current_idx = end_idx
        is_obs_period = not is_obs_period
        
        if step > 20:  # 防止无限循环
            print("  ... (超过20步，停止打印)")
            break
    
    print(f"\n掩码生成结果:")
    print(f"  总段落数: {len(segments)}")
    print(f"  第一个段落: {segments[0][0]} (t={segments[0][1]}-{segments[0][2]}, {segments[0][3]}秒)")
    if len(segments) > 1:
        print(f"  第二个段落: {segments[1][0]} (t={segments[1][1]}-{segments[1][2]}, {segments[1][3]}秒)")
    
    # 统计观测和间歇时间
    obs_time = np.sum(full_trajectory_mask == 1.0)
    gap_time = np.sum(full_trajectory_mask == 0.0)
    print(f"  观测时间: {obs_time} 秒 ({obs_time/60:.1f} 分钟, {obs_time/total_len*100:.1f}%)")
    print(f"  间歇时间: {gap_time} 秒 ({gap_time/60:.1f} 分钟, {gap_time/total_len*100:.1f}%)")
    
    return full_trajectory_mask, segments

def analyze_event_generation(full_trajectory_mask, segments):
    """分析事件生成过程"""
    print(f"\n{'='*60}")
    print(f"分析事件生成过程")
    print(f"{'='*60}")
    
    # 找到所有观测窗口的结束点作为事件点
    diff_mask = np.diff(full_trajectory_mask, prepend=0, append=0)
    event_indices = np.where(diff_mask == -1)[0] - 1  # 观测窗口结束的索引
    
    # 检查轨迹是否以观测状态结束
    if full_trajectory_mask[-1] == 1.0:
        event_indices = np.append(event_indices, len(full_trajectory_mask) - 1)
    
    print(f"事件点（观测窗口结束）: {event_indices}")
    print(f"事件点数量: {len(event_indices)}")
    
    for i, event_idx in enumerate(event_indices):
        print(f"  事件{i+1}: t={event_idx} (第{event_idx+1}秒)")
        
        # 分析这个事件点的历史
        history_mask = full_trajectory_mask[:event_idx + 1]
        obs_count = np.sum(history_mask == 1.0)
        gap_count = np.sum(history_mask == 0.0)
        
        print(f"    历史长度: {len(history_mask)} 秒")
        print(f"    观测点数: {obs_count} 个 ({obs_count/60:.1f} 分钟)")
        print(f"    间歇点数: {gap_count} 个 ({gap_count/60:.1f} 分钟)")
        
        # 检查是否从观测开始
        first_obs_idx = np.where(history_mask > 0)[0]
        if len(first_obs_idx) > 0:
            starts_with_obs = first_obs_idx[0] == 0
            print(f"    是否从观测开始: {'✅' if starts_with_obs else '❌'}")
            if not starts_with_obs:
                print(f"    第一个观测点位置: t={first_obs_idx[0]} (前{first_obs_idx[0]}秒是间歇期)")
        
        print()
    
    return event_indices

def main():
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    
    # 选择一个轨迹文件进行分析
    data_dir = Path("data/trajectories_with_env")
    trajectory_files = list(data_dir.glob("*.csv"))
    
    if not trajectory_files:
        print("没有找到轨迹文件！")
        return
    
    # 分析前3个文件
    for i, file_path in enumerate(trajectory_files[:3]):
        print(f"\n{'#'*80}")
        print(f"分析文件 {i+1}: {file_path.name}")
        print(f"{'#'*80}")
        
        # 1. 分析原始轨迹文件
        df, total_len = analyze_raw_trajectory_file(file_path)
        
        # 2. 模拟掩码生成
        full_trajectory_mask, segments = simulate_mask_generation(total_len, config, file_path)
        
        # 3. 分析事件生成
        event_indices = analyze_event_generation(full_trajectory_mask, segments)
        
        print(f"\n总结:")
        print(f"  原始轨迹长度: {total_len} 秒 ({total_len/60:.1f} 分钟)")
        print(f"  生成的事件数: {len(event_indices)} 个")
        print(f"  第一个段落类型: {segments[0][0]}")
        
        if i < 2:  # 不是最后一个文件
            input("\n按回车键继续分析下一个文件...")

if __name__ == "__main__":
    main()
