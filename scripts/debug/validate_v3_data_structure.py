#!/usr/bin/env python3
"""
验证当前数据结构是否符合V3模型的预期
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import lmdb
import pickle
import torch
from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'WenQuanYi Zen Hei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_sample_from_lmdb(lmdb_path, sample_idx=0):
    """从LMDB中加载一个样本"""
    env = lmdb.open(lmdb_path, readonly=True)
    with env.begin(write=False) as txn:
        cursor = txn.cursor()
        keys = [key for key, _ in cursor]
        if sample_idx >= len(keys):
            sample_idx = 0
        
        byteflow = txn.get(keys[sample_idx])
        sample_data = pickle.loads(byteflow)
    env.close()
    return sample_data

def validate_v3_data_structure(sample_data, sample_id):
    """验证数据结构是否符合V3模型预期"""
    
    print(f"\n{'='*60}")
    print(f"验证样本 {sample_id} 的V3数据结构")
    print(f"{'='*60}")
    
    # 获取数据
    history_features = sample_data['history_features']
    history_mask = sample_data['history_mask']
    environment_roi = sample_data['environment_roi']
    ground_truth_trajectory = sample_data['ground_truth_trajectory']
    ground_truth_destination = sample_data['ground_truth_destination']
    
    print(f"数据形状验证:")
    print(f"  history_features: {history_features.shape} (期望: (3600, 3))")
    print(f"  history_mask: {history_mask.shape} (期望: (3600,))")
    print(f"  environment_roi: {environment_roi.shape} (期望: (3600, 13, 9, 9))")
    print(f"  ground_truth_trajectory: {ground_truth_trajectory.shape} (期望: (1200, 2))")
    print(f"  ground_truth_destination: {ground_truth_destination.shape} (期望: (2,))")
    
    # 验证数据类型
    print(f"\n数据类型验证:")
    print(f"  history_features: {type(history_features)} {history_features.dtype}")
    print(f"  history_mask: {type(history_mask)} {history_mask.dtype}")
    print(f"  environment_roi: {type(environment_roi)} {environment_roi.dtype}")
    
    # 分析历史掩码
    valid_points = np.sum(history_mask > 0)
    invalid_points = np.sum(history_mask == 0)
    
    print(f"\n历史掩码分析:")
    print(f"  有效观测点: {valid_points} 个 ({valid_points/60:.1f} 分钟)")
    print(f"  间歇/填充点: {invalid_points} 个 ({invalid_points/60:.1f} 分钟)")
    print(f"  有效数据比例: {valid_points/len(history_mask)*100:.1f}%")
    
    # 找到第一个和最后一个有效点
    valid_indices = np.where(history_mask > 0)[0]
    if len(valid_indices) > 0:
        first_valid = valid_indices[0]
        last_valid = valid_indices[-1]
        print(f"  第一个有效点位置: t={first_valid} ({first_valid/60:.1f} 分钟)")
        print(f"  最后一个有效点位置: t={last_valid} ({last_valid/60:.1f} 分钟)")
        print(f"  有效数据时间跨度: {(last_valid-first_valid+1)/60:.1f} 分钟")
    
    # 验证V3模型的关键要求
    print(f"\nV3模型兼容性验证:")
    
    # 1. 检查是否有足够的观测数据
    if valid_points >= 30:  # 至少30秒的观测数据
        print(f"  ✅ 观测数据充足 ({valid_points} >= 30)")
    else:
        print(f"  ❌ 观测数据不足 ({valid_points} < 30)")
    
    # 2. 检查最后一个有效点是否可以作为预测起点
    if len(valid_indices) > 0:
        last_obs_point = history_features[last_valid]
        print(f"  ✅ 最后观测点可用作预测起点: ({last_obs_point[0]:.2f}, {last_obs_point[1]:.2f}, {last_obs_point[2]:.2f})")
    else:
        print(f"  ❌ 没有有效的观测点")
    
    # 3. 检查环境ROI数据
    non_zero_env = np.sum(np.abs(environment_roi) > 1e-6)
    total_env = environment_roi.size
    print(f"  环境ROI非零元素: {non_zero_env}/{total_env} ({non_zero_env/total_env*100:.1f}%)")
    
    # 4. 模拟V3模型的处理过程
    print(f"\n模拟V3模型处理:")
    
    # 转换为torch张量（模拟模型输入）
    history_features_torch = torch.from_numpy(history_features).float().unsqueeze(0)  # (1, 3600, 3)
    history_mask_torch = torch.from_numpy(history_mask).float().unsqueeze(0)  # (1, 3600)
    
    # 计算有效长度（V3模型中的pack_padded_sequence会用到）
    lengths = history_mask_torch.sum(dim=1).cpu().to(torch.int64)
    print(f"  计算的序列长度: {lengths.item()}")
    
    # 提取最后观测点（V3模型解码器的初始输入）
    last_obs_indices = history_mask_torch.sum(dim=1).long() - 1
    last_obs_point = history_features_torch[0, last_obs_indices[0], :]
    print(f"  解码器初始输入: ({last_obs_point[0]:.3f}, {last_obs_point[1]:.3f}, {last_obs_point[2]:.3f})")
    
    return {
        'valid_points': valid_points,
        'first_valid': first_valid if len(valid_indices) > 0 else -1,
        'last_valid': last_valid if len(valid_indices) > 0 else -1,
        'is_compatible': valid_points >= 30 and len(valid_indices) > 0
    }

def create_visualization(sample_data, stats, save_path):
    """创建数据结构可视化"""
    
    history_mask = sample_data['history_mask']
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 图1: 掩码时间线
    ax1.set_title('V3模型数据结构：历史掩码时间线', fontsize=14, fontweight='bold')
    
    time_axis = np.arange(len(history_mask))
    
    # 绘制掩码
    ax1.fill_between(time_axis, 0, 1, where=(history_mask > 0), 
                     color='green', alpha=0.3, label='观测数据')
    ax1.fill_between(time_axis, 0, 1, where=(history_mask == 0), 
                     color='red', alpha=0.2, label='间歇/填充')
    
    # 标记关键点
    valid_indices = np.where(history_mask > 0)[0]
    if len(valid_indices) > 0:
        first_valid = valid_indices[0]
        last_valid = valid_indices[-1]
        
        ax1.axvline(x=first_valid, color='blue', linestyle='--', linewidth=2, 
                   label=f'首次观测 (t={first_valid})')
        ax1.axvline(x=last_valid, color='orange', linestyle='--', linewidth=2, 
                   label=f'最后观测 (t={last_valid})')
    
    ax1.set_xlabel('时间步 (秒)')
    ax1.set_ylabel('观测状态')
    ax1.set_ylim(-0.1, 1.1)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 图2: 数据密度分析
    ax2.set_title('数据密度分析（每分钟观测点数）', fontsize=14, fontweight='bold')
    
    # 按分钟统计观测点数
    minutes = len(history_mask) // 60
    obs_per_minute = []
    
    for i in range(minutes):
        start_idx = i * 60
        end_idx = min((i + 1) * 60, len(history_mask))
        obs_count = np.sum(history_mask[start_idx:end_idx] > 0)
        obs_per_minute.append(obs_count)
    
    minute_axis = np.arange(minutes)
    bars = ax2.bar(minute_axis, obs_per_minute, alpha=0.7, 
                   color=['green' if count > 0 else 'red' for count in obs_per_minute])
    
    ax2.set_xlabel('时间 (分钟)')
    ax2.set_ylabel('观测点数')
    ax2.set_title(f'总计: {np.sum(obs_per_minute)} 个观测点，分布在 {minutes} 分钟内')
    ax2.grid(True, alpha=0.3)
    
    # 添加统计信息
    total_obs = np.sum(obs_per_minute)
    active_minutes = np.sum(np.array(obs_per_minute) > 0)
    ax2.text(0.02, 0.98, f'总观测点: {total_obs}\n活跃分钟: {active_minutes}/{minutes}\n平均密度: {total_obs/minutes:.1f} 点/分钟', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def main():
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    
    # 数据路径
    data_dir = Path(config.data_preprocessing.output_path)
    train_lmdb = data_dir / 'train'
    stats_file = data_dir / 'normalization_stats.pkl'
    
    # 加载归一化统计数据
    with open(stats_file, 'rb') as f:
        stats = pickle.load(f)
    
    print("🔍 V3模型数据结构验证")
    print("="*60)
    print("V3模型设计理念:")
    print("- 使用GRU处理完整的60分钟历史序列")
    print("- 通过history_mask区分观测和间歇期")
    print("- 右对齐填充确保'当前时刻'在序列末尾")
    print("- 零填充表示'更早时间没有数据'")
    
    # 验证多个样本
    compatible_count = 0
    total_samples = 5
    
    for i in range(total_samples):
        sample_data = load_sample_from_lmdb(str(train_lmdb), sample_idx=i)
        result = validate_v3_data_structure(sample_data, i+1)
        
        if result['is_compatible']:
            compatible_count += 1
        
        # 为第一个样本创建可视化
        if i == 0:
            save_path = f"v3_data_structure_sample_{i+1}.png"
            create_visualization(sample_data, stats, save_path)
            print(f"  可视化已保存到: {save_path}")
    
    print(f"\n{'='*60}")
    print(f"📊 V3模型兼容性总结")
    print(f"{'='*60}")
    print(f"测试样本数: {total_samples}")
    print(f"兼容样本数: {compatible_count}")
    print(f"兼容率: {compatible_count/total_samples*100:.1f}%")
    
    if compatible_count == total_samples:
        print("🎉 所有样本都与V3模型兼容！")
        print("✅ 当前的数据预处理逻辑是正确的")
        print("✅ 右对齐填充符合V3模型的设计预期")
        print("✅ 可以直接用于V3模型训练")
    else:
        print("⚠️  部分样本可能存在兼容性问题")
    
    print(f"\n💡 关键理解:")
    print(f"- '从间歇期开始'是正确的设计，表示早期时间没有数据")
    print(f"- V3模型通过GRU和掩码机制能够正确处理这种结构")
    print(f"- 60分钟的回顾窗口为模型提供了充足的上下文信息")

if __name__ == "__main__":
    main()
