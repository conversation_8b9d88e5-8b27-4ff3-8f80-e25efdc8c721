#!/usr/bin/env python3
"""
可视化数据预处理结果，验证观测-预测逻辑是否正确
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import lmdb
import pickle
import logging
from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'WenQuanYi Zen Hei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_sample_from_lmdb(lmdb_path, sample_idx=0):
    """从LMDB中加载一个样本"""
    env = lmdb.open(lmdb_path, readonly=True)
    with env.begin(write=False) as txn:
        cursor = txn.cursor()
        keys = [key for key, _ in cursor]
        if sample_idx >= len(keys):
            sample_idx = 0
        
        byteflow = txn.get(keys[sample_idx])
        sample_data = pickle.loads(byteflow)
    env.close()
    return sample_data

def denormalize_data(data, stats, key_prefix):
    """反归一化数据"""
    mean_x = stats[f"{key_prefix}_mean"]['x']
    std_x = stats[f"{key_prefix}_std"]['x']
    mean_y = stats[f"{key_prefix}_mean"]['y']
    std_y = stats[f"{key_prefix}_std"]['y']
    
    denorm_data = data.copy()
    denorm_data[..., 0] = data[..., 0] * std_x + mean_x
    denorm_data[..., 1] = data[..., 1] * std_y + mean_y
    return denorm_data

def visualize_sample_structure(sample_data, stats, save_path):
    """可视化单个样本的数据结构"""

    # 获取数据
    history_features = sample_data['history_features']
    history_mask = sample_data['history_mask']
    future_trajectory = sample_data['ground_truth_trajectory']
    destination = sample_data['ground_truth_destination']

    # 反归一化数据
    future_denorm = denormalize_data(future_trajectory, stats, 'target')
    dest_denorm = denormalize_data(destination.reshape(1, -1), stats, 'target')[0]

    # 创建两个子图（合并第二和第三个图）
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 图1: 完整轨迹结构可视化
    ax1.set_title('完整轨迹结构', fontsize=14, fontweight='bold')

    # 分离观测点和间歇点，并检查数据有效性
    obs_points = []
    gap_segments = []  # 存储间歇期的连续段

    # 首先找到所有有效的历史点（非零且非NaN）
    valid_indices = []
    for i in range(len(history_mask)):
        if i < len(history_features):
            point = history_features[i, :2]
            # 检查点是否有效（非零且非NaN）
            if not (np.isnan(point).any() or (point == 0).all()):
                valid_indices.append(i)

    # 处理有效点
    for i in valid_indices:
        point = denormalize_data(history_features[i:i+1, :2], stats, 'history')[0]
        if history_mask[i] > 0:  # 观测点
            obs_points.append(point)

    # 绘制观测点（绿色）
    if obs_points:
        obs_points = np.array(obs_points)
        ax1.plot(obs_points[:, 0], obs_points[:, 1], 'go-', linewidth=2,
                markersize=4, label=f'观测轨迹 ({len(obs_points)}点)', alpha=0.8, zorder=3)

    # 绘制未来轨迹（蓝色线）
    ax1.plot(future_denorm[:, 0], future_denorm[:, 1], 'b-', linewidth=3,
            label='未来轨迹 (20分钟)', alpha=0.9, zorder=4)

    # 绘制目的地（红色星号）
    ax1.plot(dest_denorm[0], dest_denorm[1], 'r*', markersize=20,
            label='预测目的地', zorder=5)

    # 连接最后观测点和未来轨迹起点
    if obs_points is not None and len(obs_points) > 0:
        last_obs = obs_points[-1]
        first_future = future_denorm[0]
        ax1.plot([last_obs[0], first_future[0]], [last_obs[1], first_future[1]],
                'r--', linewidth=2, alpha=0.7, label='预测连接线', zorder=4)

    ax1.set_xlabel('X坐标 (米)')
    ax1.set_ylabel('Y坐标 (米)')
    ax1.legend(loc='best')
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 图2: 时间序列和观测窗口详细分析
    ax2.set_title('观测模式时间线分析', fontsize=14, fontweight='bold')

    # 找到观测窗口
    obs_windows = []
    in_obs = False
    start_idx = 0

    for i, mask in enumerate(history_mask):
        if mask > 0 and not in_obs:  # 观测开始
            start_idx = i
            in_obs = True
        elif mask == 0 and in_obs:  # 观测结束
            obs_windows.append((start_idx, i-1))
            in_obs = False

    # 处理轨迹以观测状态结束的情况
    if in_obs:
        obs_windows.append((start_idx, len(history_mask)-1))

    # 创建时间轴
    max_history_len = len(history_mask)
    time_axis = np.arange(max_history_len)

    # 绘制基础掩码线
    ax2.plot(time_axis, history_mask, 'k-', linewidth=1, alpha=0.7)

    # 绘制观测窗口为彩色区域，并标注持续时间
    colors = ['green', 'darkgreen', 'forestgreen', 'limegreen', 'seagreen']
    for i, (start, end) in enumerate(obs_windows):
        duration = end - start + 1
        color = colors[i % len(colors)]

        # 填充观测窗口
        ax2.fill_between(range(start, end+1), 0, 1,
                        color=color, alpha=0.4,
                        label=f'观测窗口{i+1} ({duration}秒)')

        # 在窗口中间标注持续时间
        mid_point = (start + end) / 2
        ax2.text(mid_point, 0.5, f'{duration}秒\n({duration/60:.1f}分钟)',
                ha='center', va='center', fontsize=9, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    # 填充间歇期
    ax2.fill_between(time_axis, 0, 1, where=(history_mask == 0),
                     color='red', alpha=0.2, label='间歇期')

    # 添加未来轨迹时间范围
    future_start = max_history_len
    future_end = future_start + len(future_trajectory)
    ax2.axvspan(future_start, future_end, alpha=0.3, color='blue',
               label='未来轨迹 (20分钟)')

    # 标记预测开始点（最后观测结束的地方）
    if obs_windows:
        last_obs_end = obs_windows[-1][1]
        ax2.axvline(x=last_obs_end, color='red', linestyle='--', linewidth=3,
                   label=f'预测开始点 (t={last_obs_end})')

    ax2.set_xlabel('时间步 (秒)')
    ax2.set_ylabel('观测状态 (1=观测, 0=间歇)')
    ax2.set_ylim(-0.1, 1.2)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left', bbox_to_anchor=(0, 1), fontsize=9)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print(f"\n=== 样本数据结构分析 ===")
    print(f"历史长度: {len(history_mask)} 个时间步 ({len(history_mask)/60:.1f} 分钟)")
    print(f"观测点数: {np.sum(history_mask > 0)} 个 ({np.sum(history_mask > 0)/60:.1f} 分钟)")
    print(f"间歇点数: {np.sum(history_mask == 0)} 个 ({np.sum(history_mask == 0)/60:.1f} 分钟)")
    print(f"未来轨迹长度: {len(future_trajectory)} 个时间步 ({len(future_trajectory)/60:.1f} 分钟)")
    print(f"观测窗口数量: {len(obs_windows)} 个")
    print(f"文件路径: {sample_data.get('file_path', 'N/A')}")

    # 分析数据开始模式
    first_obs_idx = np.where(history_mask > 0)[0]
    if len(first_obs_idx) > 0:
        first_obs_start = first_obs_idx[0]
        print(f"第一个观测点位置: t={first_obs_start} ({'轨迹开始就是观测' if first_obs_start == 0 else f'前{first_obs_start}秒是间歇期'})")

    # 分析预测开始点
    if obs_windows:
        last_obs_end = obs_windows[-1][1]
        print(f"预测开始时刻: t={last_obs_end} (最后观测窗口结束)")

    # 打印观测窗口详细信息
    print(f"\n观测窗口详细信息:")
    for i, (start, end) in enumerate(obs_windows):
        duration = end - start + 1
        print(f"  窗口{i+1}: t={start}-{end} ({duration}秒, {duration/60:.1f}分钟)")

    # 分析间歇期
    gap_periods = []
    if obs_windows:
        # 第一个间歇期（如果存在）
        if obs_windows[0][0] > 0:
            gap_periods.append((0, obs_windows[0][0]-1))

        # 中间的间歇期
        for i in range(len(obs_windows)-1):
            gap_start = obs_windows[i][1] + 1
            gap_end = obs_windows[i+1][0] - 1
            if gap_end >= gap_start:
                gap_periods.append((gap_start, gap_end))

    if gap_periods:
        print(f"\n间歇期详细信息:")
        for i, (start, end) in enumerate(gap_periods):
            duration = end - start + 1
            print(f"  间歇期{i+1}: t={start}-{end} ({duration}秒, {duration/60:.1f}分钟)")

def main():
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    
    # 数据路径
    data_dir = Path(config.data_preprocessing.output_path)
    train_lmdb = data_dir / 'train'
    stats_file = data_dir / 'normalization_stats.pkl'
    
    # 加载归一化统计数据
    with open(stats_file, 'rb') as f:
        stats = pickle.load(f)
    
    # 加载并可视化几个样本
    for i in range(3):
        print(f"\n{'='*60}")
        print(f"正在可视化第 {i+1} 个样本")
        print(f"{'='*60}")

        sample_data = load_sample_from_lmdb(str(train_lmdb), sample_idx=i)
        save_path = f"sample_{i+1}_structure_chinese.png"

        visualize_sample_structure(sample_data, stats, save_path)
        print(f"可视化结果已保存到: {save_path}")

if __name__ == "__main__":
    main()
