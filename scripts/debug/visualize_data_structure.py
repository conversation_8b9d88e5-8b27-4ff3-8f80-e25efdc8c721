#!/usr/bin/env python3
"""
可视化数据预处理结果，验证观测-预测逻辑是否正确
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import lmdb
import pickle
import logging
from src.utils.config_loader import load_config

# 设置字体为英文
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def load_sample_from_lmdb(lmdb_path, sample_idx=0):
    """从LMDB中加载一个样本"""
    env = lmdb.open(lmdb_path, readonly=True)
    with env.begin(write=False) as txn:
        cursor = txn.cursor()
        keys = [key for key, _ in cursor]
        if sample_idx >= len(keys):
            sample_idx = 0
        
        byteflow = txn.get(keys[sample_idx])
        sample_data = pickle.loads(byteflow)
    env.close()
    return sample_data

def denormalize_data(data, stats, key_prefix):
    """反归一化数据"""
    mean_x = stats[f"{key_prefix}_mean"]['x']
    std_x = stats[f"{key_prefix}_std"]['x']
    mean_y = stats[f"{key_prefix}_mean"]['y']
    std_y = stats[f"{key_prefix}_std"]['y']
    
    denorm_data = data.copy()
    denorm_data[..., 0] = data[..., 0] * std_x + mean_x
    denorm_data[..., 1] = data[..., 1] * std_y + mean_y
    return denorm_data

def visualize_sample_structure(sample_data, stats, save_path):
    """Visualize the structure of a single sample"""

    # Get data
    history_features = sample_data['history_features']
    history_mask = sample_data['history_mask']
    future_trajectory = sample_data['ground_truth_trajectory']
    destination = sample_data['ground_truth_destination']

    # Denormalize all data
    future_denorm = denormalize_data(future_trajectory, stats, 'target')
    dest_denorm = denormalize_data(destination.reshape(1, -1), stats, 'target')[0]

    # Create figure with 3 subplots
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 6))
    
    # Plot 1: Complete trajectory visualization
    ax1.set_title('Complete Trajectory Structure', fontsize=14, fontweight='bold')

    # Separate observation and gap points
    obs_points = []
    gap_points = []

    for i, is_obs in enumerate(history_mask):
        if i < len(history_features):
            point = denormalize_data(history_features[i:i+1, :2], stats, 'history')[0]
            if is_obs > 0:
                obs_points.append(point)
            else:
                gap_points.append(point)

    # Plot observation points (green)
    if obs_points:
        obs_points = np.array(obs_points)
        ax1.scatter(obs_points[:, 0], obs_points[:, 1], c='green', s=30,
                   label=f'Observed Points ({len(obs_points)})', alpha=0.8, zorder=3)

    # Plot gap points (gray)
    if gap_points:
        gap_points = np.array(gap_points)
        ax1.scatter(gap_points[:, 0], gap_points[:, 1], c='gray', s=10,
                   label=f'Gap Points ({len(gap_points)})', alpha=0.5, zorder=2)

    # Plot future trajectory (blue line)
    ax1.plot(future_denorm[:, 0], future_denorm[:, 1], 'b-', linewidth=3,
            label='Future Trajectory (20 min)', alpha=0.9, zorder=4)

    # Plot destination (red star)
    ax1.plot(dest_denorm[0], dest_denorm[1], 'r*', markersize=20,
            label='Predicted Destination', zorder=5)

    # Connect last observation to future start
    if obs_points is not None and len(obs_points) > 0:
        last_obs = obs_points[-1]
        first_future = future_denorm[0]
        ax1.plot([last_obs[0], first_future[0]], [last_obs[1], first_future[1]],
                'r--', linewidth=2, alpha=0.7, label='Prediction Connection', zorder=4)

    ax1.set_xlabel('X Coordinate (meters)')
    ax1.set_ylabel('Y Coordinate (meters)')
    ax1.legend(loc='best')
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # Plot 2: Time series mask visualization
    ax2.set_title('Observation/Gap Pattern Over Time', fontsize=14, fontweight='bold')

    # Create time axis
    max_history_len = len(history_mask)
    time_axis = np.arange(max_history_len)

    # Plot mask as line
    ax2.plot(time_axis, history_mask, 'k-', linewidth=1, alpha=0.7)
    ax2.fill_between(time_axis, 0, history_mask, where=(history_mask > 0),
                     color='green', alpha=0.3, label='Observation Windows')
    ax2.fill_between(time_axis, 0, 1, where=(history_mask == 0),
                     color='red', alpha=0.2, label='Gap Periods')

    # Mark the event point (end of last observation)
    event_idx = sample_data.get('event_idx', -1)
    if event_idx >= 0 and event_idx < len(history_mask):
        ax2.axvline(x=event_idx, color='orange', linestyle='--', linewidth=2,
                   label=f'Event Point (idx={event_idx})')

    # Add future trajectory time range
    future_start = max_history_len
    future_end = future_start + len(future_trajectory)
    ax2.axvspan(future_start, future_end, alpha=0.3, color='blue',
               label='Future Trajectory Period')

    ax2.set_xlabel('Time Step (seconds)')
    ax2.set_ylabel('Observation State (1=Observed, 0=Gap)')
    ax2.set_ylim(-0.1, 1.1)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='best')

    # Plot 3: Observation windows analysis
    ax3.set_title('Observation Windows Detail', fontsize=14, fontweight='bold')

    # Find observation windows
    obs_windows = []
    in_obs = False
    start_idx = 0

    for i, mask in enumerate(history_mask):
        if mask > 0 and not in_obs:  # Start of observation
            start_idx = i
            in_obs = True
        elif mask == 0 and in_obs:  # End of observation
            obs_windows.append((start_idx, i-1))
            in_obs = False

    # Handle case where trajectory ends in observation
    if in_obs:
        obs_windows.append((start_idx, len(history_mask)-1))

    # Plot observation windows as horizontal bars
    for i, (start, end) in enumerate(obs_windows):
        duration = end - start + 1
        ax3.barh(i, duration, left=start, height=0.6,
                color='green', alpha=0.7,
                label='Observation Window' if i == 0 else "")
        ax3.text(start + duration/2, i, f'{duration}s',
                ha='center', va='center', fontsize=10, fontweight='bold')

    # Mark the event point
    event_idx = sample_data.get('event_idx', -1)
    if event_idx >= 0:
        ax3.axvline(x=event_idx, color='orange', linestyle='--', linewidth=2,
                   label=f'Prediction Start (t={event_idx})')

    # Add future period
    future_start = len(history_mask)
    future_end = future_start + len(future_trajectory)
    ax3.axvspan(future_start, future_end, alpha=0.3, color='blue',
               label='Future Period (20min)')

    ax3.set_xlabel('Time Step (seconds)')
    ax3.set_ylabel('Observation Window #')
    ax3.set_title(f'Total: {len(obs_windows)} Observation Windows')
    ax3.grid(True, alpha=0.3)
    ax3.legend(loc='best')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print statistics
    print(f"\n=== Sample Data Structure Analysis ===")
    print(f"History length: {len(history_mask)} time steps ({len(history_mask)/60:.1f} minutes)")
    print(f"Observed points: {np.sum(history_mask > 0)} ({np.sum(history_mask > 0)/60:.1f} minutes)")
    print(f"Gap points: {np.sum(history_mask == 0)} ({np.sum(history_mask == 0)/60:.1f} minutes)")
    print(f"Future trajectory length: {len(future_trajectory)} time steps ({len(future_trajectory)/60:.1f} minutes)")
    print(f"Number of observation windows: {len(obs_windows)}")
    print(f"File path: {sample_data.get('file_path', 'N/A')}")
    print(f"Event index: {sample_data.get('event_idx', 'N/A')}")

    # Print observation windows details
    print(f"\nObservation Windows Details:")
    for i, (start, end) in enumerate(obs_windows):
        duration = end - start + 1
        print(f"  Window {i+1}: t={start}-{end} ({duration} seconds, {duration/60:.1f} minutes)")

def main():
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    
    # 数据路径
    data_dir = Path(config.data_preprocessing.output_path)
    train_lmdb = data_dir / 'train'
    stats_file = data_dir / 'normalization_stats.pkl'
    
    # 加载归一化统计数据
    with open(stats_file, 'rb') as f:
        stats = pickle.load(f)
    
    # Load and visualize several samples
    for i in range(3):
        print(f"\n{'='*60}")
        print(f"VISUALIZING SAMPLE {i+1}")
        print(f"{'='*60}")

        sample_data = load_sample_from_lmdb(str(train_lmdb), sample_idx=i)
        save_path = f"sample_{i+1}_structure_detailed.png"

        visualize_sample_structure(sample_data, stats, save_path)
        print(f"Visualization saved to: {save_path}")

if __name__ == "__main__":
    main()
