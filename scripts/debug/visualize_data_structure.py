#!/usr/bin/env python3
"""
可视化数据预处理结果，验证观测-预测逻辑是否正确
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import lmdb
import pickle
import logging
from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_sample_from_lmdb(lmdb_path, sample_idx=0):
    """从LMDB中加载一个样本"""
    env = lmdb.open(lmdb_path, readonly=True)
    with env.begin(write=False) as txn:
        cursor = txn.cursor()
        keys = [key for key, _ in cursor]
        if sample_idx >= len(keys):
            sample_idx = 0
        
        byteflow = txn.get(keys[sample_idx])
        sample_data = pickle.loads(byteflow)
    env.close()
    return sample_data

def denormalize_data(data, stats, key_prefix):
    """反归一化数据"""
    mean_x = stats[f"{key_prefix}_mean"]['x']
    std_x = stats[f"{key_prefix}_std"]['x']
    mean_y = stats[f"{key_prefix}_mean"]['y']
    std_y = stats[f"{key_prefix}_std"]['y']
    
    denorm_data = data.copy()
    denorm_data[..., 0] = data[..., 0] * std_x + mean_x
    denorm_data[..., 1] = data[..., 1] * std_y + mean_y
    return denorm_data

def visualize_sample_structure(sample_data, stats, save_path):
    """可视化单个样本的数据结构"""
    
    # 反归一化数据
    history_features = sample_data['history_features']
    history_mask = sample_data['history_mask']
    future_trajectory = sample_data['ground_truth_trajectory']
    destination = sample_data['ground_truth_destination']
    
    # 找到有效的历史点
    valid_mask = history_mask > 0
    valid_history = history_features[valid_mask]
    
    if len(valid_history) == 0:
        print("没有有效的历史数据")
        return
    
    # 反归一化
    history_denorm = denormalize_data(valid_history[:, :2], stats, 'history')
    future_denorm = denormalize_data(future_trajectory, stats, 'target')
    dest_denorm = denormalize_data(destination.reshape(1, -1), stats, 'target')[0]
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左图：轨迹可视化
    ax1.set_title('轨迹结构可视化', fontsize=14, fontweight='bold')
    
    # 绘制历史轨迹（观测+间歇）
    obs_points = []
    gap_points = []
    
    for i, is_obs in enumerate(history_mask):
        if is_obs > 0 and i < len(history_features):
            point = denormalize_data(history_features[i:i+1, :2], stats, 'history')[0]
            obs_points.append(point)
        elif is_obs == 0 and i < len(history_features):
            point = denormalize_data(history_features[i:i+1, :2], stats, 'history')[0]
            gap_points.append(point)
    
    # 绘制观测点（绿色）
    if obs_points:
        obs_points = np.array(obs_points)
        ax1.plot(obs_points[:, 0], obs_points[:, 1], 'go-', linewidth=2, 
                markersize=4, label='观测轨迹', alpha=0.8)
    
    # 绘制间歇点（灰色）
    if gap_points:
        gap_points = np.array(gap_points)
        ax1.plot(gap_points[:, 0], gap_points[:, 1], 'o-', color='gray', 
                linewidth=1, markersize=2, label='间歇轨迹', alpha=0.6)
    
    # 绘制未来轨迹（蓝色）
    ax1.plot(future_denorm[:, 0], future_denorm[:, 1], 'b-', linewidth=2, 
            label='未来轨迹 (20分钟)', alpha=0.8)
    
    # 绘制目的地（红色星号）
    ax1.plot(dest_denorm[0], dest_denorm[1], 'r*', markersize=15, 
            label='预测目的地')
    
    # 连接最后观测点和未来轨迹起点
    if obs_points is not None and len(obs_points) > 0:
        last_obs = obs_points[-1]
        first_future = future_denorm[0]
        ax1.plot([last_obs[0], first_future[0]], [last_obs[1], first_future[1]], 
                'r--', linewidth=2, alpha=0.7, label='预测起点连接')
    
    ax1.set_xlabel('X 坐标 (米)')
    ax1.set_ylabel('Y 坐标 (米)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 右图：时间序列可视化
    ax2.set_title('时间序列结构', fontsize=14, fontweight='bold')
    
    # 创建时间轴
    max_history_len = len(history_mask)
    time_axis = np.arange(max_history_len)
    
    # 绘制掩码
    colors = ['red' if mask == 0 else 'green' for mask in history_mask]
    ax2.scatter(time_axis, history_mask, c=colors, alpha=0.6, s=20)
    
    # 添加观测/间歇区域
    in_obs = False
    start_idx = 0
    
    for i, mask in enumerate(history_mask):
        if mask > 0 and not in_obs:  # 开始观测
            start_idx = i
            in_obs = True
        elif mask == 0 and in_obs:  # 结束观测
            rect = patches.Rectangle((start_idx, -0.1), i - start_idx, 1.2, 
                                   linewidth=1, edgecolor='green', facecolor='green', alpha=0.2)
            ax2.add_patch(rect)
            in_obs = False
    
    # 如果最后还在观测状态
    if in_obs:
        rect = patches.Rectangle((start_idx, -0.1), len(history_mask) - start_idx, 1.2, 
                               linewidth=1, edgecolor='green', facecolor='green', alpha=0.2)
        ax2.add_patch(rect)
    
    ax2.set_xlabel('时间步 (秒)')
    ax2.set_ylabel('观测状态 (1=观测, 0=间歇)')
    ax2.set_ylim(-0.2, 1.2)
    ax2.grid(True, alpha=0.3)
    
    # 添加未来轨迹时间范围
    future_start = max_history_len
    future_end = future_start + len(future_trajectory)
    future_time = np.arange(future_start, future_end)
    ax2.axvspan(future_start, future_end, alpha=0.3, color='blue', label='未来轨迹时间范围')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print(f"\n=== 样本数据结构分析 ===")
    print(f"历史长度: {len(history_mask)} 个时间步")
    print(f"观测点数: {np.sum(history_mask > 0)} 个")
    print(f"间歇点数: {np.sum(history_mask == 0)} 个")
    print(f"未来轨迹长度: {len(future_trajectory)} 个时间步 ({len(future_trajectory)/60:.1f} 分钟)")
    print(f"文件路径: {sample_data.get('file_path', 'N/A')}")
    print(f"事件索引: {sample_data.get('event_idx', 'N/A')}")

def main():
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    
    # 数据路径
    data_dir = Path(config.data_preprocessing.output_path)
    train_lmdb = data_dir / 'train'
    stats_file = data_dir / 'normalization_stats.pkl'
    
    # 加载归一化统计数据
    with open(stats_file, 'rb') as f:
        stats = pickle.load(f)
    
    # 加载几个样本进行可视化
    for i in range(3):
        print(f"\n{'='*50}")
        print(f"可视化第 {i+1} 个样本")
        print(f"{'='*50}")
        
        sample_data = load_sample_from_lmdb(str(train_lmdb), sample_idx=i)
        save_path = f"sample_{i+1}_structure.png"
        
        visualize_sample_structure(sample_data, stats, save_path)
        print(f"可视化结果已保存到: {save_path}")

if __name__ == "__main__":
    main()
